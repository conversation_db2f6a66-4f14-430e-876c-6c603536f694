:header = <<
Content-Type: application/json
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36
Authorization: eyJhbGciOiJIUzI1NiJ9.eyJpZCI6MSwidXQiOjMsImV4cCI6MTc1NzA0MTM5Mn0.0ms5HldJ_qan7yufeaGWRidw9lOqL2_044m63Ooxn1E

#
# :host = https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1
:host = http://localhost:8091/api/v1

#

GET https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1/d/medias
Authorization: 90:e5:b1:a9:8c:7c

#
GET :host/m/daily-stats
:header

#
POST http://localhost:8091/api/v1/m/sms-code
:header

{
  "mobile": "16710245700"
}

#
POST http://localhost:8091/api/v1/m/login
:header

{
  "mobile": "16710245700",
  "code": "098555"
}

#
POST :host/m/push
:header

{
  "scene": "放学后"
}

#
GET :host/devices/wakeup?device_id=90:e5:b1:a8:ca:1c
:header
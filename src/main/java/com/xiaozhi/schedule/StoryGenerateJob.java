package com.xiaozhi.schedule;

import com.xiaozhi.dialogue.llm.providers.CozeChatModel;
import com.xiaozhi.service.FileResourceService;
import com.xiaozhi.service.MediaService;
import com.xiaozhi.utils.AzureSpeechUtil;
import com.xiaozhi.utils.ImageUtil;
import com.xiaozhi.utils.JsonUtil;
import com.xiaozhi.vo.MediaCreateParams;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.net.URL;
import java.nio.file.Paths;
import java.util.UUID;

@Slf4j
@Component
public class StoryGenerateJob {

    @Resource
    private MediaService mediaService;

    @Resource
    private FileResourceService fileResourceService;

    // @Scheduled(fixedDelay = 30 * 1000)
    public void subscribe() {
        var chatModel = new CozeChatModel("7542792580560863295", "sat_DKYZIpfVz7NPw3i3U88msfgwWye4uZbtCn4M1tZkC94nIG9wIVnKjtbdiUA3Yzbq");
        var topic = "指纹的唯一性是如何形成的？";
        var resp = chatModel.call(topic);

        if (StringUtils.isBlank(resp)) return;

        JsonUtil.parse(resp, Content.class)
                .onFailure(e -> log.error(e.getMessage()))
                .onSuccess(it -> {
                    var media = new MediaCreateParams()
                            .setTitle(topic)
                            .setCategory("story");

                    log.info("Story is {}", it);

                    AzureSpeechUtil.speakSsml(it.story, "zh-CN-XiaoyiNeural", "zh-CN")
                            .flatMap(filepath -> Try.of(() -> new FileInputStream(filepath))
                                    .flatMap(is -> fileResourceService.upload("story", Paths.get(filepath).getFileName().toString(), is).toTry()))
                            .peek(media::setAssetId);

                    ImageUtil.generate(it.cover, "512*512")
                            .flatMap(url -> Try.of(() -> new URL(url).openStream()))
                            .flatMap(is -> ImageUtil.png2jpg(is, 0.8f))
                            .flatMap(is -> fileResourceService.upload("story", STR."\{UUID.randomUUID().toString()}.jpg", is).toTry())
                            .peek(media::setCoverId);

                    mediaService.create(media);
                });
    }

    record Content(String story, String cover) {
    }

}

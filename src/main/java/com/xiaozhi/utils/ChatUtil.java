package com.xiaozhi.utils;

import com.xiaozhi.dialogue.llm.tool.XiaoZhiToolCallingManager;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.model.SimpleApiKey;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.http.client.JdkClientHttpRequestFactory;
import org.springframework.http.client.reactive.JdkClientHttpConnector;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

import java.net.http.HttpClient;
import java.time.Duration;

import static com.xiaozhi.dialogue.llm.Prompts.DAILY_REPORT_PROMPT;

public class ChatUtil {

    public static String complete(String level, String chatHistory) {
        var openAiApi = OpenAiApi.builder()
                .apiKey(new SimpleApiKey("sk-524899872860401da9a7222baeea1803"))
                .baseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1")
                .completionsPath("/chat/completions")
                .webClientBuilder(WebClient.builder()
                        // Force HTTP/1.1 for streaming
                        .clientConnector(new JdkClientHttpConnector(HttpClient.newBuilder()
                                .version(HttpClient.Version.HTTP_1_1)
                                .connectTimeout(Duration.ofSeconds(30))
                                .build())))
                .restClientBuilder(RestClient.builder()
                        // Force HTTP/1.1 for non-streaming
                        .requestFactory(new JdkClientHttpRequestFactory(HttpClient.newBuilder()
                                .version(HttpClient.Version.HTTP_1_1)
                                .connectTimeout(Duration.ofSeconds(30))
                                .build())))
                .build();

        var openAiChatOptions = OpenAiChatOptions.builder()
                .model("qwen-plus")
                .build();

        var chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .toolCallingManager(XiaoZhiToolCallingManager.builder().build())
                .build();

        var systemMessage = new SystemMessage(DAILY_REPORT_PROMPT.replace("{{level}}", level));
        var userMessage = new UserMessage(chatHistory);

        return chatModel.call(systemMessage, userMessage);
    }

    public static String cleanJson(String json) {
        if (json.startsWith("```json")) {
            json = json.substring(7);
        }
        if (json.startsWith("```")) {
            json = json.substring(3);
        }
        if (json.endsWith("```")) {
            json = json.substring(0, json.length() - 3);
        }
        return json.trim();
    }
}

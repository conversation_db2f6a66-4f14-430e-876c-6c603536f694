package com.xiaozhi.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.dao.DailyStudyStatsMapper;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.dao.MessageMapper;
import com.xiaozhi.dto.MessageDuration;
import com.xiaozhi.dto.Report;
import com.xiaozhi.entity.DailyStudyStats;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.entity.SysMessage;
import com.xiaozhi.service.DailyStudyStatsService;
import com.xiaozhi.utils.ChatUtil;
import com.xiaozhi.utils.JsonUtil;
import com.xiaozhi.vo.DailyStudyStatsVO;
import io.swagger.v3.core.util.Json;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DailyStudyStatsServiceImpl implements DailyStudyStatsService {

    @Resource
    private DailyStudyStatsMapper statsMapper;

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private MessageMapper messageMapper;

    private Map<String, List<String>> WordMap = new HashMap<>() {{
        put("A1", List.of(
                "hello",
                "hi",
                "bye",
                "good morning",
                "good night",
                "yes",
                "no",
                "red",
                "blue",
                "green",
                "yellow",
                "black",
                "white",
                "apple",
                "banana",
                "orange",
                "bread",
                "milk",
                "water",
                "mother",
                "father",
                "sister",
                "brother",
                "baby",
                "one",
                "two",
                "three",
                "four",
                "five"));
    }};

    @Override
    public DailyStudyStatsVO statsOf(Integer managerId) {
        // stats of today
        var statsQuery = new OhMyLambdaQueryWrapper<DailyStudyStats>()
                .eq(DailyStudyStats::getManagerId, managerId)
                .gt(DailyStudyStats::getDate, LocalDate.now().minusMonths(1));
        var monthStats = statsMapper.selectList(statsQuery);

        var todayStats = monthStats.stream()
                .filter(it -> it.getDate().isEqual(LocalDate.now()))
                .findFirst();

        var yesterday = monthStats.stream()
                .filter(it -> it.getDate().isEqual(LocalDate.now().minusDays(1)))
                .findFirst();

        var managerQuery = new OhMyLambdaQueryWrapper<Manager>()
                .eq(Manager::getId, managerId)
                .select(Manager::getId, Manager::getCefr, Manager::getDeviceId);
        var manager = managerMapper.selectOne(managerQuery);

        // create stats if not exists
        var stats = todayStats.orElseGet(() -> {
            var entity = new DailyStudyStats()
                    .setDate(LocalDate.now())
                    .setManagerId(managerId)
                    .setTopics("[]")
                    .setWords("[]")
                    .setSentences("[]")
                    .setGrammars("[]");

            // accumulate last stats
            yesterday.ifPresent(it -> {
                entity.setWordCount(it.getWordCount());
                entity.setSentenceCount(it.getSentenceCount());
            });

            statsMapper.insert(entity);
            return entity;
        });

        // calc speaking time
        var speakingQuery = new OhMyLambdaQueryWrapper<SysMessage>()
                .eq(SysMessage::getDeviceId, manager.getDeviceId())
                .gt(SysMessage::getId, Optional.ofNullable(stats.getLastMessageId()))
                .gt(SysMessage::getCreatedAt, LocalDate.now().atStartOfDay());
        var duration = Optional.ofNullable(messageMapper.findDuration(speakingQuery));

        var speakingTime = duration.map(it -> ChronoUnit.SECONDS.between(it.getBegin(), it.getEnd())).orElse(0L);

        Thread.startVirtualThread(() -> {

            var messageQuery = new OhMyLambdaQueryWrapper<SysMessage>()
                    .eq(SysMessage::getDeviceId, manager.getDeviceId())
                    .gt(SysMessage::getCreatedAt, LocalDate.now().atStartOfDay())
                    .select(SysMessage::getId, SysMessage::getSender, SysMessage::getContent);
            var messages = messageMapper.selectList(messageQuery)
                    .stream()
                    .map(it -> STR."\{it.getSender()}: \{it.getContent().trim()}")
                    .collect(Collectors.joining("\n"));
            var rawJson = ChatUtil.complete(manager.getCefr(), messages);
            log.debug("Daily Report response is {}", rawJson);
            var result = JsonUtil.fromJson(ChatUtil.cleanJson(rawJson), Report.class);
            var updateQuery = new LambdaUpdateWrapper<DailyStudyStats>()
                    .eq(DailyStudyStats::getId, stats.getId())
                    .setIncrBy(DailyStudyStats::getSpeakingTime, speakingTime)
                    .set(DailyStudyStats::getWordIncCount, result.getWords().size())
                    .set(DailyStudyStats::getSentenceIncCount, result.getCount())
                    .set(DailyStudyStats::getSuggestion, result.getSuggestion())
                    .set(DailyStudyStats::getWords, JsonUtil.toJson(result.getWords()))
                    .set(DailyStudyStats::getTopics, JsonUtil.toJson(result.getTopics()))
                    .set(DailyStudyStats::getSentences, JsonUtil.toJson(result.getSentences()))
                    .set(DailyStudyStats::getGrammars, JsonUtil.toJson(result.getGrammars()));

            duration.ifPresent(it -> updateQuery.set(DailyStudyStats::getLastMessageId, it.getId()));
            statsMapper.update(updateQuery);

        });

        // daily word charts
        var vocabularies = monthStats.stream()
                .map(it -> new DailyStudyStatsVO.DailyVocabulary(it.getDate(), it.getWordCount() + it.getWordIncCount()))
                .collect(Collectors.toList());

        var ret = new DailyStudyStatsVO()
                .setSentenceCount(stats.getSentenceCount())
                .setWords(JsonUtil.fromJson(stats.getWords(), List.class))
                .setSentences(JsonUtil.fromJson(stats.getSentences(), List.class))
                .setTopics(JsonUtil.fromJson(stats.getTopics(), List.class))
                .setGrammars(JsonUtil.fromJson(stats.getGrammars(), List.class))
                .setSuggestion(stats.getSuggestion())
                .setVocabularies(vocabularies)
                .setListeningTime(stats.getListeningTime())
                .setSpeakingTime(speakingTime + stats.getSpeakingTime());

        yesterday.ifPresent(it -> ret.setSentenceIncCount(stats.getSentenceIncCount() - it.getSentenceIncCount()));

        return ret;
    }

    public DailyStudyStatsVO statsOf2(Integer managerId) {
        // stats of today
        var statsQuery = new OhMyLambdaQueryWrapper<DailyStudyStats>()
                .eq(DailyStudyStats::getManagerId, managerId)
                .gt(DailyStudyStats::getDate, LocalDate.now().minusMonths(1));
        var monthStats = statsMapper.selectList(statsQuery);

        var todayStats = monthStats.stream()
                .filter(it -> it.getDate().isEqual(LocalDate.now()))
                .findFirst();

        // create stats if not exists
        var stats = todayStats.orElseGet(() -> {
            var entity = new DailyStudyStats()
                    .setDate(LocalDate.now())
                    .setManagerId(managerId);

            // accumulate last stats
            monthStats.stream()
                    .filter(it -> it.getDate().isEqual(LocalDate.now().minusDays(1)))
                    .findFirst()
                    .ifPresent(it -> {
                        entity.setWordCount(it.getWordCount());
                        entity.setSentenceCount(it.getSentenceCount());
                    });

            statsMapper.insert(entity);
            return entity;
        });

        var managerQuery = new OhMyLambdaQueryWrapper<Manager>()
                .eq(Manager::getId, managerId)
                .select(Manager::getId, Manager::getCefr, Manager::getDeviceId);
        var manager = managerMapper.selectOne(managerQuery);

        // calc speaking time
        var speakingQuery = new OhMyLambdaQueryWrapper<SysMessage>()
                .eq(SysMessage::getDeviceId, manager.getDeviceId())
                .gt(SysMessage::getId, Optional.ofNullable(stats.getLastMessageId()))
                .gt(SysMessage::getCreatedAt, LocalDate.now().atStartOfDay());
        var duration = Optional.ofNullable(messageMapper.findDuration(speakingQuery));

        var speakingTime = duration.map(it -> ChronoUnit.SECONDS.between(it.getBegin(), it.getEnd())).orElse(0L);

        var messageQuery = new OhMyLambdaQueryWrapper<SysMessage>()
                .eq(SysMessage::getDeviceId, manager.getDeviceId())
                .gt(SysMessage::getId, duration.map(MessageDuration::getId))
                .gt(SysMessage::getCreatedAt, LocalDate.now().atStartOfDay())
                .select(SysMessage::getId, SysMessage::getContent);
        var messages = messageMapper.selectList(messageQuery)
                .stream()
                .map(it -> it.getContent().trim())
                .toList();

        // match words
        var cefrWords = WordMap.getOrDefault(manager.getCefr(), List.of());
        var matchedWords = matchWords(messages, cefrWords);
        var oldWords = stats.getWords() == null ? List.<String>of() : List.of(stats.getWords().split(","));
        var words = union(oldWords, matchedWords);

        // match sentences

        // daily word charts
        var vocabularies = monthStats.stream()
                .map(it -> new DailyStudyStatsVO.DailyVocabulary(it.getDate(), it.getWordCount() + it.getWordIncCount()))
                .collect(Collectors.toList());

        var updateQuery = new LambdaUpdateWrapper<DailyStudyStats>()
                .eq(DailyStudyStats::getId, stats.getId())
                .setIncrBy(DailyStudyStats::getSpeakingTime, speakingTime)
                .set(DailyStudyStats::getWordIncCount, words.size())
                .set(DailyStudyStats::getSentenceIncCount, 0)
                .set(DailyStudyStats::getWords, String.join(",", words));
        duration.ifPresent(it -> updateQuery.set(DailyStudyStats::getLastMessageId, it.getId()));
        statsMapper.update(updateQuery);

        return new DailyStudyStatsVO()
                .setWords(words)
                .setSentences(List.of())
                .setVocabularies(vocabularies)
                .setListeningTime(stats.getListeningTime())
                .setSpeakingTime(speakingTime + stats.getSpeakingTime());
    }

    @Override
    public Either<BizError, ?> updateListeningTime(Integer managerId) {
        var query = new OhMyLambdaQueryWrapper<DailyStudyStats>()
                .eq(DailyStudyStats::getManagerId, managerId)
                .eq(DailyStudyStats::getDate, LocalDate.now())
                .select(DailyStudyStats::getId);

        return Option.of(statsMapper.selectOne(query))
                .toEither(BizError.ResourceNotFound)
                .map(it -> {
                    var updateQuery = new LambdaUpdateWrapper<DailyStudyStats>()
                            .eq(DailyStudyStats::getId, it.getId())
                            .setIncrBy(DailyStudyStats::getListeningTime, 10);
                    statsMapper.update(updateQuery);
                    return true;
                });
    }

    private List<String> matchWords(List<String> sentences, List<String> words) {
        var text = String.join(" ", sentences);

        return words.stream()
                .map(it -> matchWord(text, it))
                .filter(it -> !it.isEmpty())
                .collect(Collectors.toList());
    }

    private String matchWord(String text, String word) {
        var p = Pattern.compile(STR."\\b\{Pattern.quote(word)}\\b",
                Pattern.CASE_INSENSITIVE);
        return p.matcher(text).find() ? word : "";
    }

    private List<String> union(List<String> xs, List<String> ys) {
        var set = new LinkedHashSet<>(xs);
        set.addAll(ys);
        return new ArrayList<>(set);
    }
}
